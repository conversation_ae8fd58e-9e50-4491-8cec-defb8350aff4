<script lang="ts">
  import { userStore } from '$lib/stores';
  import GameCanvas from '$lib/components/GameCanvas.svelte';
  import TaskForm from '$lib/components/TaskForm.svelte';
  import TaskList from '$lib/components/TaskList.svelte';

  // Overlay state
  let showTaskForm = false;
  let showTaskList = false;

  function toggleTaskForm() {
    showTaskForm = !showTaskForm;
    if (showTaskForm) showTaskList = false; // Close other overlay
  }

  function toggleTaskList() {
    showTaskList = !showTaskList;
    if (showTaskList) showTaskForm = false; // Close other overlay
  }

  function closeOverlays() {
    showTaskForm = false;
    showTaskList = false;
  }
</script>

<main class="fullscreen-container">
  <!-- Fixed header with controls -->
  <header class="fixed-header">
    <div class="header-left">
      <h1>🍌 Banana Checklist</h1>
    </div>

    <div class="header-center">
      <div class="toggle-buttons">
        <button
          class="toggle-btn"
          class:active={showTaskForm}
          on:click={toggleTaskForm}
          title="Add New Task"
        >
          ➕ Add Task
        </button>
        <button
          class="toggle-btn"
          class:active={showTaskList}
          on:click={toggleTaskList}
          title="View Tasks"
        >
          📋 Tasks
        </button>
      </div>
    </div>

    <div class="header-right">
      <div class="banana-counter">
        <span class="banana-icon">🍌</span>
        <span class="banana-count">{$userStore.bananaCount}</span>
      </div>
    </div>
  </header>

  <!-- Fullscreen game canvas -->
  <section class="game-section">
    <GameCanvas />
  </section>

  <!-- Task Form Overlay -->
  {#if showTaskForm}
    <div class="overlay" on:click={closeOverlays} role="button" tabindex="0" on:keydown={(e) => e.key === 'Escape' && closeOverlays()}>
      <div class="overlay-content task-form-overlay" on:click|stopPropagation on:keydown|stopPropagation role="dialog" tabindex="0">
        <div class="overlay-header">
          <h2>➕ Add New Task</h2>
          <button class="close-btn" on:click={closeOverlays}>✕</button>
        </div>
        <TaskForm />
      </div>
    </div>
  {/if}

  <!-- Task List Overlay -->
  {#if showTaskList}
    <div class="overlay" on:click={closeOverlays} role="button" tabindex="0" on:keydown={(e) => e.key === 'Escape' && closeOverlays()}>
      <div class="overlay-content task-list-overlay" on:click|stopPropagation on:keydown|stopPropagation role="dialog" tabindex="0">
        <div class="overlay-header">
          <h2>📋 Your Tasks</h2>
          <button class="close-btn" on:click={closeOverlays}>✕</button>
        </div>
        <TaskList />
      </div>
    </div>
  {/if}
</main>

<style>
  :global(body) {
    margin: 0;
    padding: 0;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .fullscreen-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #10B981, #059669);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 100;
  }

  .header-left h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;
  }

  .toggle-buttons {
    display: flex;
    gap: 0.5rem;
  }

  .toggle-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
  }

  .toggle-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  .toggle-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #10B981;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .header-right {
    display: flex;
    align-items: center;
  }

  .banana-counter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: bold;
    backdrop-filter: blur(10px);
  }

  .banana-icon {
    font-size: 1.3rem;
  }

  .game-section {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background: #228B22;
    overflow: hidden;
  }

  /* Overlay styles */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .overlay-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
  }

  .task-form-overlay {
    width: 90%;
    max-width: 500px;
  }

  .task-list-overlay {
    width: 90%;
    max-width: 700px;
  }

  .overlay-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 2px solid #E5E7EB;
    background: linear-gradient(135deg, #F9FAFB, #F3F4F6);
    border-radius: 16px 16px 0 0;
  }

  .overlay-header h2 {
    margin: 0;
    color: #374151;
    font-size: 1.5rem;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6B7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .close-btn:hover {
    background: #F3F4F6;
    color: #374151;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @media (max-width: 768px) {
    .fixed-header {
      height: 70px;
      padding: 0 0.5rem;
    }

    .header-left h1 {
      font-size: 1.2rem;
    }

    .toggle-buttons {
      flex-direction: column;
      gap: 0.25rem;
    }

    .toggle-btn {
      padding: 0.4rem 0.8rem;
      font-size: 0.8rem;
    }

    .banana-counter {
      font-size: 1rem;
      padding: 0.4rem 0.8rem;
    }

    .game-section {
      top: 70px;
    }

    .overlay-content {
      width: 95%;
      max-height: 85vh;
    }

    .overlay-header {
      padding: 1rem;
    }

    .overlay-header h2 {
      font-size: 1.3rem;
    }
  }
</style>
